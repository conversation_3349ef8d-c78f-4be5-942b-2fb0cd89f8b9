# Backend Dockerfile for Closet Glass Chic
FROM node:18-slim AS base

# Install system dependencies including Python and build tools
RUN apt-get update && apt-get install -y \
    curl \
    dumb-init \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    libjpeg-dev \
    zlib1g-dev \
    libffi-dev \
    libcairo2-dev \
    libpango1.0-dev \
    libgdk-pixbuf2.0-dev \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Create non-root user
RUN groupadd -g 1001 appgroup && \
    useradd -u 1001 -g appgroup -m appuser

# Development stage
FROM base AS development

# Copy package files
COPY backend/package*.json ./
RUN npm install && npm install --platform=linux --arch=x64 sharp

# Install Python dependencies
COPY backend/python/requirements.txt ./python/
RUN cd python && pip3 install --break-system-packages -r requirements.txt

# Copy source code (excluding node_modules)
COPY backend/src/ ./src/
COPY backend/tsconfig.json ./
COPY backend/vitest.config.ts ./

# Copy Python scripts
COPY backend/python/ ./python/

# Set permissions for uploads directory
RUN mkdir -p uploads/clothing && \
    chown -R appuser:appgroup /app/uploads && \
    chmod -R 755 /app/uploads

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 3001

# Start development server
CMD ["dumb-init", "npm", "run", "dev"]

# Build stage
FROM base AS builder

# Copy package files and install all dependencies
COPY backend/package*.json ./
RUN npm install

# Copy source code (excluding node_modules)
COPY backend/src/ ./src/
COPY backend/tsconfig.json ./
COPY backend/vitest.config.ts ./

# Build the application
RUN npm run build

# Production stage
FROM base AS production

# Copy package files and install only production dependencies
COPY backend/package*.json ./
RUN npm install --only=production && npm cache clean --force

# Install Python dependencies
COPY backend/python/requirements.txt ./python/
RUN cd python && pip3 install --break-system-packages -r requirements.txt

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package.json ./

# Copy Python scripts
COPY backend/python/ ./python/

# Create uploads directory
RUN mkdir -p uploads

# Switch to non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Expose port
EXPOSE 3001

# Start the application
CMD ["dumb-init", "node", "dist/index.js"]
