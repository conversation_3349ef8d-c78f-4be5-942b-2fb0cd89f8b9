import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { spawn } from 'child_process';
// import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger.js';
import { initializeDatabase } from '../database/connection.js';
import { ColorExtractionService } from '../services/colorExtractionApi.js';
import { ClothDetectionService } from '../services/clothDetectionApi.js';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'clothing');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error as Error, '');
    }
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.'));
    }
  }
});

// Helper function to run external API analysis
const runExternalAnalysis = async (imageBuffer: Buffer, filename: string): Promise<any> => {
  try {
    // Initialize services if not already done
    ColorExtractionService.initialize();
    ClothDetectionService.initialize();

    // Run both APIs in parallel for better performance
    const [colorResult, clothResult] = await Promise.all([
      ColorExtractionService.extractColor(imageBuffer, filename),
      ClothDetectionService.detectClothing(imageBuffer, filename)
    ]);

    // Get the best category from detection results
    const category = ClothDetectionService.getBestCategory(clothResult.detections);

    return {
      success: true,
      analysis: {
        category: category,
        color: colorResult.color_name
      },
      colorDetails: {
        hex: colorResult.hex,
        rgb: colorResult.rgb
      },
      detections: clothResult.detections,
      processingTime: clothResult.processing_time_sec
    };
  } catch (error) {
    logger.error('EXTERNAL_ANALYSIS', 'Failed to analyze with external APIs', {
      error: error instanceof Error ? error.message : 'Unknown error',
      filename
    }, error instanceof Error ? error : undefined);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// POST /api/clothing/upload - Upload and analyze clothing image
router.post('/upload', upload.single('image'), async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'No image file provided',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('CLOTHING_UPLOAD', 'Processing clothing image upload', {
      requestId,
      filename: req.file.filename,
      size: req.file.size
    });

    // Read the uploaded image file into a buffer for external API calls
    const imageBuffer = await fs.readFile(req.file.path);

    // Run external API analysis
    const analysisResult = await runExternalAnalysis(imageBuffer, req.file.filename);

    if (!analysisResult.success) {
      logger.error('CLOTHING_ANALYSIS', 'Failed to analyze clothing', {
        requestId,
        error: analysisResult.error
      });
      
      return res.status(500).json({
        error: 'Failed to analyze clothing image',
        details: analysisResult.error,
        timestamp: new Date().toISOString()
      });
    }

    // Generate URL for the original image
    const originalUrl = `/uploads/clothing/${req.file.filename}`;

    logger.info('CLOTHING_ANALYSIS', 'Successfully analyzed clothing', {
      requestId,
      category: analysisResult.analysis.category,
      color: analysisResult.analysis.color,
      colorHex: analysisResult.colorDetails?.hex,
      detectionsCount: analysisResult.detections?.length
    });

    res.json({
      success: true,
      data: {
        originalImage: originalUrl,
        processedImage: originalUrl, // Use original image since we don't process it anymore
        analysis: analysisResult.analysis,
        colorDetails: analysisResult.colorDetails,
        detections: analysisResult.detections,
        processingTime: analysisResult.processingTime
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('CLOTHING_UPLOAD', 'Error processing clothing upload', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined);

    res.status(500).json({
      error: 'Internal server error during image processing',
      timestamp: new Date().toISOString()
    });
  }
});

// POST /api/clothing - Save clothing item to database
router.post('/', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  
  try {
    const {
      userId,
      name,
      category,
      color,
      brand,
      size,
      season,
      imageUrl,
      processedImageUrl
    } = req.body;

    // Validate required fields
    if (!userId || !category || !color) {
      return res.status(400).json({
        error: 'Missing required fields: userId, category, color',
        timestamp: new Date().toISOString()
      });
    }

    const db = await initializeDatabase();

    // Get category ID
    const categoryResult = await db.query(
      'SELECT id FROM clothing_categories WHERE name = $1',
      [category]
    );

    let categoryId = null;
    if (categoryResult.rows.length > 0) {
      categoryId = categoryResult.rows[0].id;
    }

    // Insert clothing item
    const result = await db.query(`
      INSERT INTO clothing_items (
        user_id, category_id, name, color, brand, size, 
        image_url, tags, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [
      userId,
      categoryId,
      name || `${brand || 'Unknown'} ${category}`,
      color,
      brand,
      size,
      processedImageUrl || imageUrl,
      [season].filter(Boolean) // Add season as tag if provided
    ]);

    const clothingItem = result.rows[0];

    logger.info('CLOTHING_SAVE', 'Successfully saved clothing item', {
      requestId,
      clothingItemId: clothingItem.id,
      category,
      color
    });

    res.status(201).json({
      success: true,
      data: clothingItem,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('CLOTHING_SAVE', 'Error saving clothing item', {
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined);

    res.status(500).json({
      error: 'Internal server error while saving clothing item',
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/clothing/:userId - Get all clothing items for a user
router.get('/:userId', async (req, res) => {
  const requestId = req.headers['x-request-id'] as string;
  const { userId } = req.params;
  
  try {
    const db = await initializeDatabase();

    const result = await db.query(`
      SELECT 
        ci.*,
        cc.name as category_name
      FROM clothing_items ci
      LEFT JOIN clothing_categories cc ON ci.category_id = cc.id
      WHERE ci.user_id = $1 AND ci.status = 'active'
      ORDER BY ci.created_at DESC
    `, [userId]);

    logger.info('CLOTHING_GET', 'Retrieved clothing items', {
      requestId,
      userId,
      count: result.rows.length
    });

    res.json({
      success: true,
      data: result.rows,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('CLOTHING_GET', 'Error retrieving clothing items', {
      requestId,
      userId,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, error instanceof Error ? error : undefined);

    res.status(500).json({
      error: 'Internal server error while retrieving clothing items',
      timestamp: new Date().toISOString()
    });
  }
});

export { router as clothingRouter };
